import React from 'react';

// 图标通用属性接口
interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

// 能耗图标 - 闪电
export const EnergyConsumptionIcon: React.FC<IconProps> = ({
  width = 32,
  height = 32,
  color = '#8a8a8a',
  className,
}) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    width={width}
    height={height}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M564.544 58.048C591.488 25.6 644.032 47.808 639.808 89.6L609.728 384h243.584c36.224 0 55.936 42.24 32.768 69.952l-426.624 512c-26.944 32.384-79.488 10.24-75.2-31.616L414.336 640H170.688a42.688 42.688 0 0 1-32.768-69.952l426.624-512z"
      fill={color}
    />
  </svg>
);

// 成本图标 - 趋势图
export const EnergyCostIcon: React.FC<IconProps> = ({
  width = 32,
  height = 32,
  color = '#8a8a8a',
  className,
}) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    width={width}
    height={height}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M930 932h-804c-27.616 0-50-22.392-50-50v-752C76 102.384 98.384 80 126 80S176 102.384 176 130V832h754c27.608 0 50 22.392 50 50s-22.392 50-50 50z"
      fill={color}
    />
    <path
      d="M320 718a49.86 49.86 0 0 1-35.356-14.644c-19.524-19.528-19.524-51.184 0-70.712l136-136A50 50 0 0 1 456 482h185.288l194.356-194.356c19.524-19.528 51.188-19.528 70.712 0 19.528 19.524 19.528 51.184 0 70.712l-209 209a49.98 49.98 0 0 1-35.356 14.644h-185.288l-121.356 121.356A49.86 49.86 0 0 1 320 718z"
      fill={color}
    />
  </svg>
);

// 效率图标 - 条形图
export const EnergyEfficiencyIcon: React.FC<IconProps> = ({
  width = 32,
  height = 32,
  color = '#8a8a8a',
  className,
}) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    width={width}
    height={height}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M854.4 928H169.6C121.6 928 96 902.4 96 854.4V0H0v854.4C0 947.2 76.8 1024 169.6 1024H1024v-96h-169.6z"
      fill={color}
    />
    <path d="M160 160h608v96H160zM160 416h768v96H160zM160 672h416v96H160z" fill={color} />
  </svg>
);

// 单位能耗图标 - 波形图
export const UnitEnergyConsumptionIcon: React.FC<IconProps> = ({
  width = 32,
  height = 32,
  color = '#8a8a8a',
  className,
}) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    width={width}
    height={height}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M651.648128 791.1424L416.512128 83.1488c-14.1312-42.5984-74.1632-42.5984-88.3072 0L199.180928 471.68H46.540928A46.6304 46.6304 0 0 0 0.000128 518.4a46.6304 46.6304 0 0 0 46.5408 46.72h186.1888a46.5664 46.5664 0 0 0 44.16-31.9488l95.4752-287.5136 235.1104 707.9936c14.144 42.5984 74.176 42.5984 88.32 0l129.024-388.5312h152.64A46.6304 46.6304 0 0 0 1024.000128 518.4a46.6304 46.6304 0 0 0-46.5408-46.72H791.270528a46.5664 46.5664 0 0 0-44.16 31.9488L651.648128 791.1424z"
      fill={color}
    />
  </svg>
);
