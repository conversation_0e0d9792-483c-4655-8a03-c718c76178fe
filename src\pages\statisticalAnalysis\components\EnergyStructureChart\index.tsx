import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { ECBasicOption } from 'echarts/types/dist/shared';
import style from '../../index.module.less';

// 注册ECharts组件
echarts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 能源结构图表组件属性接口
 */
interface EnergyStructureChartProps {
  // 不需要额外的props
}

/**
 * 能源数据定义
 */
interface EnergyData {
  key: string;
  energyType: string;
  amount: number;
  percentage: number;
  unit: string;
}

/**
 * 能源结构图表组件
 *
 * 显示企业整体能源消耗的结构分布，使用饼图展示
 * 可以直观看出各类能源在总能耗中的占比情况
 * 点击"更多"按钮可查看详细的能源结构数据表格
 *
 * @param props - 组件属性
 * @returns 能源结构图表组件
 */
const EnergyStructureChart: React.FC<EnergyStructureChartProps> = () => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 各类能源的消耗量和占比（扩展到9条数据）
  const detailData: EnergyData[] = [
    { key: '1', energyType: '煤炭', amount: 621.8, percentage: 35.2, unit: '万tce' },
    { key: '2', energyType: '电力', amount: 447.7, percentage: 25.4, unit: '万tce' },
    { key: '3', energyType: '天然气', amount: 216.4, percentage: 12.3, unit: '万tce' },
    { key: '4', energyType: '石油', amount: 169.2, percentage: 9.6, unit: '万tce' },
    { key: '5', energyType: '生物质能', amount: 98.5, percentage: 5.6, unit: '万tce' },
    { key: '6', energyType: '太阳能', amount: 87.3, percentage: 4.9, unit: '万tce' },
    { key: '7', energyType: '风能', amount: 65.1, percentage: 3.7, unit: '万tce' },
    { key: '8', energyType: '水能', amount: 42.8, percentage: 2.4, unit: '万tce' },
    { key: '9', energyType: '其他', amount: 15.2, percentage: 0.9, unit: '万tce' },
  ];

  // 渐变色类型定义
  interface GradientColor {
    type: 'linear';
    x: number;
    y: number;
    x2: number;
    y2: number;
    colorStops: Array<{ offset: number; color: string }>;
  }

  // 能源类型颜色映射 - 使用更美观的渐变色系
  const energyColors: { [key: string]: GradientColor } = {
    煤炭: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#4a4a4a' },
        { offset: 1, color: '#2a2a2a' },
      ],
    },
    电力: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#40a9ff' },
        { offset: 1, color: '#1890ff' },
      ],
    },
    天然气: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#73d13d' },
        { offset: 1, color: '#52c41a' },
      ],
    },
    石油: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffc53d' },
        { offset: 1, color: '#fa8c16' },
      ],
    },
    生物质能: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#95de64' },
        { offset: 1, color: '#7cb305' },
      ],
    },
    太阳能: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffd666' },
        { offset: 1, color: '#faad14' },
      ],
    },
    风能: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#85a5ff' },
        { offset: 1, color: '#597ef7' },
      ],
    },
    水能: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#5cdbd3' },
        { offset: 1, color: '#13c2c2' },
      ],
    },
    其他: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#b37feb' },
        { offset: 1, color: '#722ed1' },
      ],
    },
  };

  // ECharts饼图配置选项 - 优化美观度和交互效果
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      // 动画配置
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',

      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        formatter: (params: { name: string; data: { amount: number }; percent: number }) => {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
              <div>消耗量: ${params.data.amount} 万tce</div>
              <div>占比: ${params.percent}%</div>
            </div>
          `;
        },
      },
      legend: {
        orient: 'horizontal',
        left: 'center',
        bottom: '8%',
        textStyle: {
          color: '#666',
          fontSize: 11,
        },
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 12,
        // 图例分两行显示，避免拥挤
        formatter: (name: string) => {
          return name.length > 3 ? `${name.substring(0, 3)}...` : name;
        },
      },
      series: [
        {
          name: '能源结构',
          type: 'pie',
          radius: ['45%', '75%'], // 调整环形饼图大小
          center: ['50%', '42%'], // 居中显示，为图例留出更多空间
          avoidLabelOverlap: true,
          // 扇区间距
          itemStyle: {
            borderRadius: 3,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: true,
            position: 'outside',
            formatter: (params: { name: string; percent: number }) => {
              // 只显示占比大于2%的标签，避免标签重叠
              return params.percent > 2 ? `${params.name}\n${params.percent}%` : '';
            },
            fontSize: 10,
            color: '#666',
            lineHeight: 14,
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 8,
            smooth: true,
            lineStyle: {
              color: '#ccc',
              width: 1,
            },
          },
          emphasis: {
            scale: true,
            scaleSize: 5,
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333',
            },
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
          data: detailData.map((item) => ({
            name: item.energyType,
            value: item.percentage,
            amount: item.amount,
            itemStyle: {
              color: energyColors[item.energyType] || {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#f0f0f0' },
                  { offset: 1, color: '#d9d9d9' },
                ],
              },
            },
          })),
        },
      ],
    }),
    [detailData, energyColors],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = (): void => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = (): void => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = (): void => {
    setIsModalVisible(false);
  };

  // 简化的颜色映射，用于表格显示
  const simpleColors: { [key: string]: string } = {
    煤炭: '#4a4a4a',
    电力: '#1890ff',
    天然气: '#52c41a',
    石油: '#fa8c16',
    生物质能: '#7cb305',
    太阳能: '#faad14',
    风能: '#597ef7',
    水能: '#13c2c2',
    其他: '#722ed1',
  };

  // 详细数据表格列配置
  const columns: Array<{
    title: string;
    dataIndex: string;
    key: string;
    render?: (value: any, record: EnergyData) => React.ReactNode;
  }> = [
    {
      title: '能源类型',
      dataIndex: 'energyType',
      key: 'energyType',
      render: (text: string) => {
        return (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span
              style={{
                width: 12,
                height: 12,
                backgroundColor: simpleColors[text] || '#d9d9d9',
                borderRadius: '50%',
                marginRight: 8,
              }}
            />
            {text}
          </span>
        );
      },
    },
    {
      title: '消耗量',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number, record: EnergyData) => `${value.toFixed(1)} ${record.unit}`,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent: React.ReactNode = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能源结构的详细数据，显示各类能源的消耗量和占比：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
      <div style={{ marginTop: 16, fontSize: 12, color: '#8c8c8c' }}>
        * 数据统计周期：当前选择的时间范围内的累计消耗量
      </div>
    </div>
  );

  return (
    <>
      <div className={style['energy-structure-chart']}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>能源结构</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="能源结构 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default EnergyStructureChart;
