import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { ECBasicOption } from 'echarts/types/dist/shared';
import style from '../../index.module.less';

// 注册ECharts组件
echarts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 企业能源构成图表组件属性接口
 */
interface EnergyCompositionChartProps {
  // 不需要额外的props
}

/**
 * 能源构成数据定义
 */
interface EnergyCompositionData {
  key: string;
  month: string;
  coal: number;
  electricity: number;
  gas: number;
  others: number;
}

/**
 * 企业能源构成图表组件
 *
 * 显示企业各类能源（煤炭、电力、天然气等）在不同时间的构成比例
 * 使用堆叠柱状图展示，可以看出能源结构的变化趋势
 * 点击"更多"按钮可查看详细的月度能源构成数据
 *
 * @param props - 组件属性
 * @returns 企业能源构成图表组件
 */
const EnergyCompositionChart: React.FC<EnergyCompositionChartProps> = () => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 各月份不同能源类型的占比
  const detailData: EnergyCompositionData[] = [
    { key: '1', month: '2024-01', coal: 45.2, electricity: 32.1, gas: 15.3, others: 7.4 },
    { key: '2', month: '2024-02', coal: 43.8, electricity: 33.5, gas: 16.1, others: 6.6 },
    { key: '3', month: '2024-03', coal: 46.1, electricity: 31.2, gas: 14.9, others: 7.8 },
    { key: '4', month: '2024-04', coal: 44.5, electricity: 32.8, gas: 15.7, others: 7.0 },
    { key: '5', month: '2024-05', coal: 45.9, electricity: 31.9, gas: 15.2, others: 7.0 },
    { key: '6', month: '2024-06', coal: 44.7, electricity: 32.4, gas: 15.8, others: 7.1 },
  ];

  // 能源类型颜色映射
  const energyColors = {
    coal: '#8c8c8c',      // 煤炭：灰色
    electricity: '#1890ff', // 电力：蓝色
    gas: '#52c41a',       // 天然气：绿色
    others: '#722ed1',    // 其他：紫色
  };

  // ECharts堆叠柱状图配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: ['煤炭', '电力', '天然气', '其他'],
        top: '5%',
        textStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 16,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: detailData.map((item) => item.month.substring(5)), // 只显示月份
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '占比(%)',
        nameTextStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: '{value}%',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '煤炭',
          type: 'bar',
          stack: 'total',
          data: detailData.map((item) => item.coal),
          itemStyle: {
            color: energyColors.coal,
          },
        },
        {
          name: '电力',
          type: 'bar',
          stack: 'total',
          data: detailData.map((item) => item.electricity),
          itemStyle: {
            color: energyColors.electricity,
          },
        },
        {
          name: '天然气',
          type: 'bar',
          stack: 'total',
          data: detailData.map((item) => item.gas),
          itemStyle: {
            color: energyColors.gas,
          },
        },
        {
          name: '其他',
          type: 'bar',
          stack: 'total',
          data: detailData.map((item) => item.others),
          itemStyle: {
            color: energyColors.others,
          },
        },
      ],
    }),
    [detailData, energyColors],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '煤炭 (%)',
      dataIndex: 'coal',
      key: 'coal',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '电力 (%)',
      dataIndex: 'electricity',
      key: 'electricity',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '天然气 (%)',
      dataIndex: 'gas',
      key: 'gas',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '其他 (%)',
      dataIndex: 'others',
      key: 'others',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是企业能源构成的详细数据，显示各类能源在不同月份的占比：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
    </div>
  );

  return (
    <>
      <div className={style['energy-composition-chart']}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>企业能源构成</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="企业能源构成 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default EnergyCompositionChart;
